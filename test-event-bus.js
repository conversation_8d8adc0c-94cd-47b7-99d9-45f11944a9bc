/**
 * Test script to verify inter-interceptor communication
 * This script can be run in the browser console to test the event bus
 */

// Test function to verify event bus functionality
function testInterceptorEventBus() {
  console.log('🧪 Testing Inter-Interceptor Event Bus...');
  
  // Check if event bus is available
  if (typeof InterceptorEventBus === 'undefined') {
    console.error('❌ InterceptorEventBus not available');
    return false;
  }
  
  if (typeof INTERCEPTOR_EVENT_TYPES === 'undefined') {
    console.error('❌ INTERCEPTOR_EVENT_TYPES not available');
    return false;
  }
  
  console.log('✅ Event bus classes available');
  
  // Create a test event bus
  const testEventBus = new InterceptorEventBus('test-client', { debug: true });
  
  // Test event subscription and emission
  let eventReceived = false;
  const unsubscribe = testEventBus.subscribe('STABLE_FRAME_DETECTED', (event) => {
    console.log('📨 Received test event:', event);
    eventReceived = true;
  }, { interceptorName: 'test-interceptor' });
  
  // Emit a test event
  testEventBus.emit('STABLE_FRAME_DETECTED', {
    interceptorName: 'change-detector',
    consecutiveStableCount: 3,
    stabilityThreshold: 1,
    requiredStableFrames: 3,
    isStabilityComplete: true,
    timestamp: Date.now()
  }, { source: 'test' });
  
  // Check if event was received
  setTimeout(() => {
    if (eventReceived) {
      console.log('✅ Event bus test passed');
    } else {
      console.error('❌ Event bus test failed - event not received');
    }
    
    // Cleanup
    unsubscribe();
    testEventBus.cleanup();
  }, 100);
  
  return true;
}

// Test function to check if interceptors are properly connected
function testInterceptorConnections() {
  console.log('🔗 Testing Interceptor Connections...');
  
  // Check if control tab manager is available
  if (typeof window.controlTabManager === 'undefined') {
    console.error('❌ Control tab manager not available');
    return false;
  }
  
  const manager = window.controlTabManager;
  
  // Check if interceptor registry is available
  if (typeof interceptorRegistry === 'undefined') {
    console.error('❌ Interceptor registry not available');
    return false;
  }
  
  console.log('✅ Control tab manager and registry available');
  
  // Check web client groups
  console.log('📊 Web client groups:', manager.webClientGroups.size);
  
  for (const [clientId, group] of manager.webClientGroups) {
    console.log(`🌐 Client ${clientId}:`, {
      hasPipeline: !!group.interceptorPipeline,
      hasEventBus: !!interceptorRegistry.clientEventBuses.get(clientId)
    });
    
    if (group.interceptorPipeline) {
      console.log(`  📊 Pipeline interceptors:`, group.interceptorPipeline.interceptors.map(i => i.name));
      console.log(`  🚌 Event bus connected:`, !!group.interceptorPipeline.eventBus);
    }
  }
  
  return true;
}

// Test function to manually trigger change detector
function testChangeDetectorTrigger() {
  console.log('🎯 Testing Change Detector Trigger...');
  
  if (typeof window.controlTabManager === 'undefined') {
    console.error('❌ Control tab manager not available');
    return false;
  }
  
  const manager = window.controlTabManager;
  
  // Find a web client with an active pipeline
  for (const [clientId, group] of manager.webClientGroups) {
    if (group.interceptorPipeline) {
      const changeDetector = group.interceptorPipeline.interceptors.find(i => i.name === 'change-detector');
      const videoCrop = group.interceptorPipeline.interceptors.find(i => i.name === 'video-crop');
      
      if (changeDetector && videoCrop) {
        console.log(`🎯 Found interceptors for client ${clientId}`);
        
        // Enable change detector
        changeDetector.updateConfig({ enabled: true });
        changeDetector.startMonitoring();
        
        console.log('✅ Change detector enabled and monitoring started');
        
        // Simulate stable frame detection after a delay
        setTimeout(() => {
          console.log('🎬 Simulating stable frame detection...');
          
          // Get current crop region before test
          const beforeCrop = videoCrop.config.cropRegion;
          console.log('📐 Crop region before:', beforeCrop);
          
          // Manually trigger stable frame event
          changeDetector.handleStableFrame();
          
          // Check crop region after a delay
          setTimeout(() => {
            const afterCrop = videoCrop.config.cropRegion;
            console.log('📐 Crop region after:', afterCrop);
            
            if (JSON.stringify(beforeCrop) !== JSON.stringify(afterCrop)) {
              console.log('✅ Crop region changed - inter-interceptor communication working!');
            } else {
              console.log('⚠️ Crop region unchanged - check event bus connection');
            }
          }, 500);
          
        }, 1000);
        
        return true;
      }
    }
  }
  
  console.error('❌ No active pipeline with change detector and video crop found');
  return false;
}

// Main test function
function runAllTests() {
  console.log('🧪 Running Inter-Interceptor Communication Tests...');
  console.log('================================================');
  
  // Test 1: Event bus functionality
  testInterceptorEventBus();
  
  // Test 2: Interceptor connections
  setTimeout(() => {
    testInterceptorConnections();
  }, 200);
  
  // Test 3: Change detector trigger (only if streaming is active)
  setTimeout(() => {
    testChangeDetectorTrigger();
  }, 500);
}

// Export for console use
window.testInterceptorEventBus = testInterceptorEventBus;
window.testInterceptorConnections = testInterceptorConnections;
window.testChangeDetectorTrigger = testChangeDetectorTrigger;
window.runAllTests = runAllTests;

console.log('🧪 Test functions loaded. Run runAllTests() to test inter-interceptor communication.');
